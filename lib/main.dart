import 'package:device_preview/device_preview.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:developer';

import 'core/constants.dart';
import 'core/providers/riverpod/theme_provider.dart';
import 'core/services/keycloak_service.dart';
import 'core/theme/app_theme.dart';
import 'routes/app_router.dart';
// EasyLife feature integration
import 'features/easylife/data/datasources/local/shared_pref_helper.dart';
// Debug maintenance service
import 'modules/payments/services/maintenance_bill_service.dart';
// OnePay integration
import 'core/services/onepay_service.dart';
import 'core/services/onepay_config_service.dart';
import 'core/services/onepay_api_service.dart';
import 'core/services/mobile_recharge_service.dart';
import 'utils/storage/sso_storage.dart';
// Configuration
import 'config/api_config.dart';
// Dependency Injection
import 'injection_container.dart' as di;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.white,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // Initialize Dependency Injection Container
  print('🔧 [DI] Initializing dependency injection...');
  await di.init();
  print('✅ [DI] Dependency injection initialized');

  // Initialize services
  await KeycloakService.initialize();

  // Initialize SharedPreferences for EasyLife
  await SharedPreferencesHelper.init();

  // Initialize secure OnePay configuration
  try {
    print('🔐 [CONFIG] Initializing secure OnePay configuration...');
    await OnePayConfig.initialize();
    print('✅ [CONFIG] OnePay configuration loaded successfully');
  } catch (e) {
    print('❌ [CONFIG] Failed to load OnePay configuration: $e');
    print(
        '💡 [CONFIG] Please create a .env file with ONEPAY_ACCESS_TOKEN and ONEPAY_API_TOKEN');
  }

  // Initialize OnePay configuration service (this fetches and stores x-access-token and x-api-token)
  log('🚀 Initializing OnePay configuration service...');
  await OnePayConfigService.instance.initialize();

  // Initialize OnePay service
  log('🚀 Initializing OnePay service...');
  await OnePayService.instance.initialize();

  // EasyLife service will be initialized when needed
  print('App starting with EasyLife integration ready');

  // Force initialize maintenance service for debugging
  print('🔍 [DEBUG] Forcing MaintenanceBillService initialization...');
  MaintenanceBillService.instance;

  runApp(
    // Wrap the entire app with ProviderScope for Riverpod
    ProviderScope(
      child: DevicePreview(
        enabled: false,
        builder: (context) => const MyApp(), // Wrap your app
      ),
    ),
  );
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the theme mode provider
    final themeMode = ref.watch(themeModeProvider);

    // For backward compatibility, we're still using AppRouter.router
    // In a new project, you would use ref.watch(routerProvider) instead
    return ScreenUtilInit(
      designSize: const Size(375, 812), // Standard design size
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp.router(
          title: AppConstants.appName,
          debugShowCheckedModeBanner: false,
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: themeMode,
          routerConfig: AppRouter.router,
        );
      },
    );
  }
}
