import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/colors.dart';
import '../../../core/theme/style_guide.dart';
import '../../../core/widgets/app_button.dart';
import '../../../common_widgets/app_card.dart';
import '../../../common_widgets/app_text_field.dart';
import '../../../core/services/sso_flutter_maintenance_service.dart';
import '../../../core/services/keycloak_service.dart';
import '../../../utils/storage/sso_storage.dart';
import '../../../data/models/recharge_models.dart';
import '../../household/maintenance/widgets/maintenance_billing_card.dart';
import '../widgets/payment_amount_breakdown.dart';
import '../widgets/payment_step_indicator.dart';
import '../widgets/maintenance_bill_card.dart';
import 'payment_history_page.dart';

enum MaintenanceStep {
  BILLS_OVERVIEW,
  ACCOUNT_DETAILS,
  AMOUNT_CALCULATION,
  PAYMENT_CONFIRMATION,
  PAYMENT_PROCESSING,
}

class EnhancedMaintenancePaymentPage extends ConsumerStatefulWidget {
  const EnhancedMaintenancePaymentPage({super.key});

  @override
  ConsumerState<EnhancedMaintenancePaymentPage> createState() =>
      _EnhancedMaintenancePaymentPageState();
}

class _EnhancedMaintenancePaymentPageState
    extends ConsumerState<EnhancedMaintenancePaymentPage>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _societyNameController = TextEditingController();
  final _accountNumberController = TextEditingController();
  final _confirmAccountController = TextEditingController();
  final _ifscController = TextEditingController();
  final _amountController = TextEditingController();
  final _panController = TextEditingController();
  final _noteController = TextEditingController();

  final _emailFocusNode = FocusNode();
  final PageController _pageController = PageController();

  MaintenanceStep _currentStep = MaintenanceStep.BILLS_OVERVIEW;
  Timer? _debounce;

  bool _isLoadingAccount = false;
  bool _isLoadingAccountSuggestions = false;
  bool _isCalculating = false;
  bool _isProcessingPayment = false;
  bool _termsAccepted = false;

  Map<String, dynamic>? _accountDetails;
  Map<String, dynamic>? _amountCalculation;
  List<Map<String, dynamic>> _accountSuggestions = [];
  List<MaintenancePayment> _maintenanceBills = [];
  String? _selectedBillId;

  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadUserData();
    _loadMaintenanceBills();

    // Listen to email changes for autocomplete
    _emailController.addListener(_onEmailChanged);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _societyNameController.dispose();
    _accountNumberController.dispose();
    _confirmAccountController.dispose();
    _ifscController.dispose();
    _amountController.dispose();
    _panController.dispose();
    _noteController.dispose();
    _emailFocusNode.dispose();
    _pageController.dispose();
    _debounce?.cancel();
    _slideController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeInOut,
    ));
  }

  void _onEmailChanged() {
    if (_debounce?.isActive ?? false) _debounce?.cancel();

    _debounce = Timer(const Duration(milliseconds: 800), () {
      final email = _emailController.text.trim();
      if (email.isNotEmpty && email.contains('@')) {
        _getAccountSuggestions(email);
      }
    });
  }

  Future<void> _loadUserData() async {
    try {
      // Try to get user data from authentication
      final userData = await KeycloakService.getUserData();
      if (userData != null && userData['email'] != null) {
        _emailController.text = userData['email'];
        _getAccountSuggestions(userData['email']);
      }
    } catch (e) {
      print('Error loading user data: $e');
    }
  }

  Future<void> _loadMaintenanceBills() async {
    try {
      final bills = await SSOFlutterMaintenanceService.getMaintenanceInvoices();

      final maintenancePayments = bills
          .map((bill) => MaintenancePayment(
                transactionId: bill['invoice_id'] ??
                    bill['id'] ??
                    DateTime.now().millisecondsSinceEpoch.toString(),
                accountId: bill['account_id'] ?? '1',
                accountName:
                    bill['account_name'] ?? bill['society_name'] ?? 'Society',
                amount: (bill['amount'] ?? bill['total_due_amount'] ?? 0.0)
                    .toDouble(),
                totalAmount:
                    (bill['total_amount'] ?? bill['total_due_amount'] ?? 0.0)
                        .toDouble(),
                status: bill['status'] ?? 'pending',
                paymentDate: bill['due_date'] != null
                    ? DateTime.tryParse(bill['due_date']) ?? DateTime.now()
                    : DateTime.now(),
                dueDate: bill['due_date'] != null
                    ? DateTime.tryParse(bill['due_date'])
                    : null,
                description: bill['description'] ?? 'Maintenance Bill',
                orderId: bill['order_id'],
              ))
          .toList();

      setState(() {
        _maintenanceBills = maintenancePayments;
      });
    } catch (e) {
      print('Error loading maintenance bills: $e');
    }
  }

  Future<void> _getAccountSuggestions(String email) async {
    setState(() {
      _isLoadingAccountSuggestions = true;
    });

    try {
      // Use SSO-Flutter compatible API to get account suggestions
      final suggestions =
          await SSOFlutterMaintenanceService.getAccountSuggestions(
              email: email);

      setState(() {
        _accountSuggestions = suggestions;
        _isLoadingAccountSuggestions = false;
      });
    } catch (e) {
      print('Error getting account suggestions: $e');
      setState(() {
        _isLoadingAccountSuggestions = false;
      });
    }
  }

  Future<void> _selectAccount(Map<String, dynamic> account) async {
    setState(() {
      _isLoadingAccount = true;
    });

    try {
      // Auto-populate fields from selected account
      _emailController.text = account['email'] ?? '';
      _societyNameController.text =
          account['business_name'] ?? account['society_name'] ?? '';
      _accountNumberController.text = account['account_number'] ?? '';
      _confirmAccountController.text = account['account_number'] ?? '';
      _ifscController.text = account['ifsc_code'] ?? '';

      setState(() {
        _accountDetails = account;
        _isLoadingAccount = false;
      });

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text('Account details loaded for ${account['business_name']}'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() {
        _isLoadingAccount = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading account: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _calculateAmount() async {
    if (!_formKey.currentState!.validate()) return;

    final amount = double.tryParse(_amountController.text);
    if (amount == null) return;

    setState(() {
      _isCalculating = true;
    });

    try {
      final calculation =
          await SSOFlutterMaintenanceService.calculateTotalSocietyPaymentAmount(
        amount: amount,
      );

      setState(() {
        _amountCalculation = calculation;
        _isCalculating = false;
      });

      _nextStep();
    } catch (e) {
      setState(() {
        _isCalculating = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error calculating amount: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _processPayment() async {
    if (!_termsAccepted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please accept terms and conditions'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isProcessingPayment = true;
      _currentStep = MaintenanceStep.PAYMENT_PROCESSING;
    });

    try {
      final result = await SSOFlutterMaintenanceService.initiateSocietyPayment(
        accountName:
            _accountDetails!['business_name'] ?? _societyNameController.text,
        totalPayableAmount: _amountCalculation!['amount'],
        actualAmount: double.parse(_amountController.text),
        accountId: _accountDetails!['account_id'] ?? '1',
        pan: _panController.text.isNotEmpty ? _panController.text : null,
        note: _noteController.text.isNotEmpty ? _noteController.text : null,
      );

      _showSuccessDialog(result);
    } catch (e) {
      _showErrorDialog(e.toString());
    } finally {
      setState(() {
        _isProcessingPayment = false;
      });
    }
  }

  void _nextStep() {
    switch (_currentStep) {
      case MaintenanceStep.BILLS_OVERVIEW:
        if (_selectedBillId != null) {
          setState(() {
            _currentStep = MaintenanceStep.ACCOUNT_DETAILS;
          });
          _pageController.nextPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
        break;
      case MaintenanceStep.ACCOUNT_DETAILS:
        if (_accountDetails != null && _formKey.currentState!.validate()) {
          setState(() {
            _currentStep = MaintenanceStep.AMOUNT_CALCULATION;
          });
          _pageController.nextPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
        break;
      case MaintenanceStep.AMOUNT_CALCULATION:
        _calculateAmount();
        break;
      case MaintenanceStep.PAYMENT_CONFIRMATION:
        _processPayment();
        break;
      case MaintenanceStep.PAYMENT_PROCESSING:
        break;
    }
  }

  void _previousStep() {
    switch (_currentStep) {
      case MaintenanceStep.BILLS_OVERVIEW:
        // Can't go back from first step
        break;
      case MaintenanceStep.ACCOUNT_DETAILS:
        setState(() {
          _currentStep = MaintenanceStep.BILLS_OVERVIEW;
        });
        _pageController.previousPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        break;
      case MaintenanceStep.AMOUNT_CALCULATION:
        setState(() {
          _currentStep = MaintenanceStep.ACCOUNT_DETAILS;
        });
        _pageController.previousPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        break;
      case MaintenanceStep.PAYMENT_CONFIRMATION:
        setState(() {
          _currentStep = MaintenanceStep.AMOUNT_CALCULATION;
        });
        _pageController.previousPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        break;
      case MaintenanceStep.ACCOUNT_DETAILS:
      case MaintenanceStep.PAYMENT_PROCESSING:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Maintenance Payment'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PaymentHistoryPage(),
                ),
              );
            },
            tooltip: 'Payment History',
          ),
        ],
      ),
      body: Column(
        children: [
          // Progress Indicator
          _buildProgressIndicator(),

          // Main Content
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildBillsOverviewStep(),
                _buildAccountDetailsStep(),
                _buildAmountCalculationStep(),
                _buildPaymentConfirmationStep(),
              ],
            ),
          ),

          // Navigation Buttons
          if (_currentStep != MaintenanceStep.PAYMENT_PROCESSING)
            _buildNavigationButtons(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      color: AppColors.primary,
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          _buildStepIndicator(0, 'Account', _currentStep.index >= 0),
          Expanded(child: _buildProgressLine(_currentStep.index >= 1)),
          _buildStepIndicator(1, 'Amount', _currentStep.index >= 1),
          Expanded(child: _buildProgressLine(_currentStep.index >= 2)),
          _buildStepIndicator(2, 'Payment', _currentStep.index >= 2),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int step, String label, bool isActive) {
    return Column(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: isActive ? Colors.white : Colors.white.withOpacity(0.3),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              '${step + 1}',
              style: TextStyle(
                color: isActive ? AppColors.primary : Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressLine(bool isActive) {
    return Container(
      height: 2,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: isActive ? Colors.white : Colors.white.withOpacity(0.3),
        borderRadius: BorderRadius.circular(1),
      ),
    );
  }

  Widget _buildBillsOverviewStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Select Maintenance Bill',
            style: AppStyleGuide.headingLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppStyleGuide.spacingS),
          Text(
            'Choose the maintenance bill you want to pay',
            style: AppStyleGuide.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),

          const SizedBox(height: AppStyleGuide.spacingL),

          // Bills List
          if (_maintenanceBills.isEmpty)
            Center(
              child: Column(
                children: [
                  const SizedBox(height: 40),
                  Icon(
                    Icons.receipt_long,
                    size: 64,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No maintenance bills found',
                    style: AppStyleGuide.headingMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Please check back later or contact support',
                    style: AppStyleGuide.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            )
          else
            ...(_maintenanceBills.map((bill) => MaintenanceBillCard(
                  billId: bill.transactionId ?? bill.accountId,
                  description: bill.description ?? 'Maintenance Bill',
                  amount: bill.amount,
                  dueDate: bill.dueDate ?? bill.paymentDate,
                  status: bill.status,
                  societyName: bill.accountName,
                  unitNumber: null, // Not available in MaintenancePayment model
                  isSelected: _selectedBillId == bill.transactionId,
                  onTap: () {
                    setState(() {
                      _selectedBillId = bill.transactionId;
                    });
                  },
                ))),
        ],
      ),
    );
  }

  Widget _buildAccountDetailsStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Account Details',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Enter your email to find existing accounts or create a new one',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),

            // Email Field with Autocomplete
            TextFormField(
              controller: _emailController,
              focusNode: _emailFocusNode,
              keyboardType: TextInputType.emailAddress,
              decoration: InputDecoration(
                labelText: 'Email Address',
                hintText: 'Enter your email address',
                prefixIcon: const Icon(Icons.email),
                suffixIcon: _isLoadingAccountSuggestions
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: Padding(
                          padding: EdgeInsets.all(8.0),
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      )
                    : null,
                border: const OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your email address';
                }
                if (!value.contains('@')) {
                  return 'Please enter a valid email address';
                }
                return null;
              },
            ),

            // Account Suggestions
            if (_accountSuggestions.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'Select an existing account:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              ...(_accountSuggestions.map((account) => Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: const Icon(Icons.account_balance,
                          color: AppColors.primary),
                      title: Text(account['business_name'] ?? 'Account'),
                      subtitle: Text(account['email'] ?? ''),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _selectAccount(account),
                    ),
                  ))),
            ],

            if (_accountDetails != null) ...[
              const SizedBox(height: 24),
              const Divider(),
              const SizedBox(height: 16),

              // Society Name
              TextFormField(
                controller: _societyNameController,
                decoration: const InputDecoration(
                  labelText: 'Society Name',
                  prefixIcon: Icon(Icons.apartment),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter society name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Bank Account Number
              TextFormField(
                controller: _accountNumberController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Bank Account Number',
                  prefixIcon: Icon(Icons.account_balance),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter bank account number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Confirm Account Number
              TextFormField(
                controller: _confirmAccountController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Confirm Bank Account Number',
                  prefixIcon: Icon(Icons.account_balance),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value != _accountNumberController.text) {
                    return 'Account numbers do not match';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // IFSC Code
              TextFormField(
                controller: _ifscController,
                decoration: const InputDecoration(
                  labelText: 'IFSC Code',
                  prefixIcon: Icon(Icons.code),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter IFSC code';
                  }
                  return null;
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAmountCalculationStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Payment Amount',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Enter the amount you want to pay',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),

          // Amount Input
          TextFormField(
            controller: _amountController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'Amount (₹)',
              prefixIcon: const Icon(Icons.currency_rupee),
              border: const OutlineInputBorder(),
              suffixIcon: _isCalculating
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: Padding(
                        padding: EdgeInsets.all(8.0),
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : null,
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter amount';
              }
              final amount = double.tryParse(value);
              if (amount == null || amount <= 0) {
                return 'Please enter a valid amount';
              }
              return null;
            },
          ),
          const SizedBox(height: 24),

          // Amount Calculation Display
          if (_amountCalculation != null)
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Payment Breakdown',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildAmountRow(
                        'Base Amount', _amountCalculation!['base_amount']),
                    _buildAmountRow(
                        'Processing Fee', _amountCalculation!['fee']),
                    _buildAmountRow('GST', _amountCalculation!['gst']),
                    const Divider(),
                    _buildAmountRow(
                      'Total Amount',
                      _amountCalculation!['amount'],
                      isTotal: true,
                    ),
                  ],
                ),
              ),
            ),

          const SizedBox(height: 24),

          // PAN Input (if amount >= 50000)
          if (_amountCalculation != null &&
              _amountCalculation!['amount'] >= 50000)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'PAN Required for amounts ≥ ₹50,000',
                  style: TextStyle(
                    color: Colors.orange,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _panController,
                  decoration: const InputDecoration(
                    labelText: 'PAN Number',
                    prefixIcon: Icon(Icons.credit_card),
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (_amountCalculation!['amount'] >= 50000 &&
                        (value == null || value.isEmpty)) {
                      return 'PAN is required for amounts ≥ ₹50,000';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
              ],
            ),

          // Notes
          TextFormField(
            controller: _noteController,
            maxLines: 3,
            decoration: const InputDecoration(
              labelText: 'Notes (Optional)',
              prefixIcon: Icon(Icons.note),
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountRow(String label, dynamic amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '₹${amount?.toStringAsFixed(2) ?? '0.00'}',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentConfirmationStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Payment Confirmation',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Please review your payment details',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),

          // Payment Summary
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Payment Summary',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildSummaryRow('Society', _societyNameController.text),
                  _buildSummaryRow('Email', _emailController.text),
                  _buildSummaryRow('Account Number',
                      '**** **** ${_accountNumberController.text.substring(_accountNumberController.text.length - 4)}'),
                  _buildSummaryRow('IFSC Code', _ifscController.text),
                  if (_panController.text.isNotEmpty)
                    _buildSummaryRow('PAN', _panController.text),
                  const Divider(),
                  _buildSummaryRow(
                    'Total Amount',
                    '₹${_amountCalculation?['amount']?.toStringAsFixed(2) ?? '0.00'}',
                    isTotal: true,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Terms and Conditions
          Row(
            children: [
              Checkbox(
                value: _termsAccepted,
                onChanged: (value) {
                  setState(() {
                    _termsAccepted = value ?? false;
                  });
                },
              ),
              const Expanded(
                child: Text(
                  'I accept the terms and conditions for this payment',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep != MaintenanceStep.ACCOUNT_DETAILS)
            Expanded(
              child: AppButton(
                label: 'Previous',
                onPressed: _previousStep,
                type: AppButtonType.outline,
              ),
            ),
          if (_currentStep != MaintenanceStep.ACCOUNT_DETAILS)
            const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: AppButton(
              label: _getNextButtonLabel(),
              onPressed: _getNextButtonAction() ?? () {},
              disabled: _getNextButtonAction() == null ||
                  _isProcessingPayment ||
                  _isCalculating ||
                  _isLoadingAccount,
            ),
          ),
        ],
      ),
    );
  }

  String _getNextButtonLabel() {
    switch (_currentStep) {
      case MaintenanceStep.BILLS_OVERVIEW:
        return 'Select Bill';
      case MaintenanceStep.ACCOUNT_DETAILS:
        return 'Continue';
      case MaintenanceStep.AMOUNT_CALCULATION:
        return _isCalculating ? 'Calculating...' : 'Calculate Amount';
      case MaintenanceStep.PAYMENT_CONFIRMATION:
        return 'Pay Now';
      case MaintenanceStep.PAYMENT_PROCESSING:
        return 'Processing...';
    }
  }

  VoidCallback? _getNextButtonAction() {
    if (_isProcessingPayment || _isCalculating || _isLoadingAccount) {
      return null;
    }

    switch (_currentStep) {
      case MaintenanceStep.BILLS_OVERVIEW:
        return _selectedBillId != null ? _nextStep : null;
      case MaintenanceStep.ACCOUNT_DETAILS:
        return _accountDetails != null ? _nextStep : null;
      case MaintenanceStep.AMOUNT_CALCULATION:
        return _amountController.text.isNotEmpty ? _nextStep : null;
      case MaintenanceStep.PAYMENT_CONFIRMATION:
        return _termsAccepted ? _nextStep : null;
      case MaintenanceStep.PAYMENT_PROCESSING:
        return null;
    }
  }

  void _showSuccessDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.green, size: 28),
            const SizedBox(width: 8),
            const Text('Payment Successful'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Transaction ID: ${result['transaction_id'] ?? 'N/A'}'),
            Text(
                'Amount: ₹${_amountCalculation?['amount']?.toStringAsFixed(2) ?? '0.00'}'),
            const SizedBox(height: 8),
            const Text('Your payment has been processed successfully.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go('/dashboard');
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red, size: 28),
            SizedBox(width: 8),
            Text('Payment Failed'),
          ],
        ),
        content: Text(error),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _currentStep = MaintenanceStep.PAYMENT_CONFIRMATION;
              });
            },
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }
}
