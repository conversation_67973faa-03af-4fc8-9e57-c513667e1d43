import '../../../core/models/base_models.dart';

/// Maintenance Calculation extending BaseModel
class MaintenanceCalculation extends BaseModel {
  final double originalAmount;
  final double calculatedAmount;
  final double processingFee;
  final double convenienceFee;
  final double totalAmount;
  final Map<String, double> breakdown;
  final String currency;

  MaintenanceCalculation({
    required this.originalAmount,
    required this.calculatedAmount,
    required this.processingFee,
    required this.convenienceFee,
    required this.totalAmount,
    this.breakdown = const {},
    this.currency = 'INR',
    super.id,
    super.createdAt,
    super.updatedAt,
    super.metadata,
  });

  factory MaintenanceCalculation.fromJson(Map<String, dynamic> json) {
    return MaintenanceCalculation(
      originalAmount: JsonHelpers.parseDouble(json['original_amount']) ?? 0.0,
      calculatedAmount:
          JsonHelpers.parseDouble(json['calculated_amount']) ?? 0.0,
      processingFee: JsonHelpers.parseDouble(json['processing_fee']) ?? 0.0,
      convenienceFee: JsonHelpers.parseDouble(json['convenience_fee']) ?? 0.0,
      totalAmount: JsonHelpers.parseDouble(json['total_amount']) ?? 0.0,
      breakdown: Map<String, double>.from(
        (json['breakdown'] ?? {}).map(
          (key, value) => MapEntry(key, JsonHelpers.parseDouble(value) ?? 0.0),
        ),
      ),
      currency: json['currency'] ?? 'INR',
      id: json['id'],
      createdAt: JsonHelpers.parseDateTime(json['created_at']),
      updatedAt: JsonHelpers.parseDateTime(json['updated_at']),
      metadata: JsonHelpers.parseMap(json['metadata']),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'original_amount': originalAmount,
      'calculated_amount': calculatedAmount,
      'processing_fee': processingFee,
      'convenience_fee': convenienceFee,
      'total_amount': totalAmount,
      'breakdown': breakdown,
      'currency': currency,
      'id': id,
      'created_at': JsonHelpers.dateTimeToJson(createdAt),
      'updated_at': JsonHelpers.dateTimeToJson(updatedAt),
      'metadata': metadata,
    };
  }
}

/// Maintenance Payment Request extending BaseRequest
class MaintenancePaymentRequest extends BaseRequest with ModelValidation {
  final String accountName;
  final String accountId;
  final double totalAmount;
  final double actualAmount;
  final String? pan;
  final String? note;
  final String paymentMethod;
  final Map<String, dynamic> additionalData;

  MaintenancePaymentRequest({
    required this.accountName,
    required this.accountId,
    required this.totalAmount,
    required this.actualAmount,
    this.pan,
    this.note,
    this.paymentMethod = 'online',
    this.additionalData = const {},
    super.id,
    super.createdAt,
    super.updatedAt,
    super.metadata,
  });

  factory MaintenancePaymentRequest.fromJson(Map<String, dynamic> json) {
    return MaintenancePaymentRequest(
      accountName: json['account_name'] ?? '',
      accountId: json['account_id'] ?? '',
      totalAmount: JsonHelpers.parseDouble(json['total_amount']) ?? 0.0,
      actualAmount: JsonHelpers.parseDouble(json['actual_amount']) ?? 0.0,
      pan: json['pan'],
      note: json['note'],
      paymentMethod: json['payment_method'] ?? 'online',
      additionalData: JsonHelpers.parseMap(json['additional_data']) ?? {},
      id: json['id'],
      createdAt: JsonHelpers.parseDateTime(json['created_at']),
      updatedAt: JsonHelpers.parseDateTime(json['updated_at']),
      metadata: JsonHelpers.parseMap(json['metadata']),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'account_name': accountName,
      'account_id': accountId,
      'total_amount': totalAmount,
      'actual_amount': actualAmount,
      'pan': pan,
      'note': note,
      'payment_method': paymentMethod,
      'additional_data': additionalData,
      'id': id,
      'created_at': JsonHelpers.dateTimeToJson(createdAt),
      'updated_at': JsonHelpers.dateTimeToJson(updatedAt),
      'metadata': metadata,
    };
  }

  @override
  bool validate() {
    final errors = getValidationErrors();
    return errors.isEmpty;
  }

  @override
  List<String> getValidationErrors() {
    final errors = <String>[];

    // Validate required fields
    final accountNameValidation =
        Validators.required(accountName, 'Account name');
    if (!accountNameValidation.isValid) {
      errors.addAll(accountNameValidation.errors);
    }

    final accountIdValidation = Validators.required(accountId, 'Account ID');
    if (!accountIdValidation.isValid) {
      errors.addAll(accountIdValidation.errors);
    }

    // Validate amounts
    final totalAmountValidation =
        Validators.positive(totalAmount, 'Total amount');
    if (!totalAmountValidation.isValid) {
      errors.addAll(totalAmountValidation.errors);
    }

    final actualAmountValidation =
        Validators.positive(actualAmount, 'Actual amount');
    if (!actualAmountValidation.isValid) {
      errors.addAll(actualAmountValidation.errors);
    }

    return errors;
  }

  @override
  bool requiresAuth() => true;

  @override
  int getTimeoutSeconds() => 60;
}

/// Complete Payment Request extending BaseRequest
class CompletePaymentRequest extends BaseRequest with ModelValidation {
  final String paymentId;
  final String transactionId;
  final String status;
  final double amount;
  final String? failureReason;
  final Map<String, dynamic> paymentDetails;

  CompletePaymentRequest({
    required this.paymentId,
    required this.transactionId,
    required this.status,
    required this.amount,
    this.failureReason,
    this.paymentDetails = const {},
    super.id,
    super.createdAt,
    super.updatedAt,
    super.metadata,
  });

  factory CompletePaymentRequest.fromJson(Map<String, dynamic> json) {
    return CompletePaymentRequest(
      paymentId: json['payment_id'] ?? '',
      transactionId: json['transaction_id'] ?? '',
      status: json['status'] ?? '',
      amount: JsonHelpers.parseDouble(json['amount']) ?? 0.0,
      failureReason: json['failure_reason'],
      paymentDetails: JsonHelpers.parseMap(json['payment_details']) ?? {},
      id: json['id'],
      createdAt: JsonHelpers.parseDateTime(json['created_at']),
      updatedAt: JsonHelpers.parseDateTime(json['updated_at']),
      metadata: JsonHelpers.parseMap(json['metadata']),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'payment_id': paymentId,
      'transaction_id': transactionId,
      'status': status,
      'amount': amount,
      'failure_reason': failureReason,
      'payment_details': paymentDetails,
      'id': id,
      'created_at': JsonHelpers.dateTimeToJson(createdAt),
      'updated_at': JsonHelpers.dateTimeToJson(updatedAt),
      'metadata': metadata,
    };
  }

  @override
  bool validate() {
    final errors = getValidationErrors();
    return errors.isEmpty;
  }

  @override
  List<String> getValidationErrors() {
    final errors = <String>[];

    // Validate required fields
    final paymentIdValidation = Validators.required(paymentId, 'Payment ID');
    if (!paymentIdValidation.isValid) {
      errors.addAll(paymentIdValidation.errors);
    }

    final transactionIdValidation =
        Validators.required(transactionId, 'Transaction ID');
    if (!transactionIdValidation.isValid) {
      errors.addAll(transactionIdValidation.errors);
    }

    final statusValidation = Validators.required(status, 'Status');
    if (!statusValidation.isValid) {
      errors.addAll(statusValidation.errors);
    }

    final amountValidation = Validators.positive(amount, 'Amount');
    if (!amountValidation.isValid) {
      errors.addAll(amountValidation.errors);
    }

    return errors;
  }

  @override
  bool requiresAuth() => true;

  @override
  int getTimeoutSeconds() => 60;
}

/// Maintenance Payment Response extending BaseModel
class MaintenancePaymentResponse extends BaseModel {
  final String paymentId;
  final String transactionId;
  final String status;
  final double amount;
  final String currency;
  final DateTime timestamp;
  final String? gatewayResponse;
  final Map<String, dynamic> paymentDetails;

  MaintenancePaymentResponse({
    required this.paymentId,
    required this.transactionId,
    required this.status,
    required this.amount,
    this.currency = 'INR',
    required this.timestamp,
    this.gatewayResponse,
    this.paymentDetails = const {},
    super.id,
    super.createdAt,
    super.updatedAt,
    super.metadata,
  });

  factory MaintenancePaymentResponse.fromJson(Map<String, dynamic> json) {
    return MaintenancePaymentResponse(
      paymentId: json['payment_id'] ?? '',
      transactionId: json['transaction_id'] ?? '',
      status: json['status'] ?? '',
      amount: JsonHelpers.parseDouble(json['amount']) ?? 0.0,
      currency: json['currency'] ?? 'INR',
      timestamp: JsonHelpers.parseDateTime(json['timestamp']) ?? DateTime.now(),
      gatewayResponse: json['gateway_response'],
      paymentDetails: JsonHelpers.parseMap(json['payment_details']) ?? {},
      id: json['id'],
      createdAt: JsonHelpers.parseDateTime(json['created_at']),
      updatedAt: JsonHelpers.parseDateTime(json['updated_at']),
      metadata: JsonHelpers.parseMap(json['metadata']),
    );
  }

  /// Create from SSO-Flutter service response
  factory MaintenancePaymentResponse.fromSsoResponse(
      Map<String, dynamic> ssoResponse) {
    return MaintenancePaymentResponse(
      paymentId: ssoResponse['payment_id'] ?? ssoResponse['id'] ?? '',
      transactionId:
          ssoResponse['transaction_id'] ?? ssoResponse['txn_id'] ?? '',
      status: ssoResponse['status'] ?? 'pending',
      amount: JsonHelpers.parseDouble(ssoResponse['amount']) ?? 0.0,
      currency: ssoResponse['currency'] ?? 'INR',
      timestamp:
          JsonHelpers.parseDateTime(ssoResponse['timestamp']) ?? DateTime.now(),
      gatewayResponse: ssoResponse['gateway_response'],
      paymentDetails: JsonHelpers.parseMap(ssoResponse['details']) ?? {},
      metadata: {
        'source': 'sso_flutter_service',
        'original_response': ssoResponse,
      },
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'payment_id': paymentId,
      'transaction_id': transactionId,
      'status': status,
      'amount': amount,
      'currency': currency,
      'timestamp': JsonHelpers.dateTimeToJson(timestamp),
      'gateway_response': gatewayResponse,
      'payment_details': paymentDetails,
      'id': id,
      'created_at': JsonHelpers.dateTimeToJson(createdAt),
      'updated_at': JsonHelpers.dateTimeToJson(updatedAt),
      'metadata': metadata,
    };
  }
}

/// Society Account extending BaseModel
class SocietyAccount extends BaseModel {
  final String accountId;
  final String businessName;
  final String societyName;
  final String flatNumber;
  final String ownerName;
  final String email;
  final String? phoneNumber;
  final String? address;
  final bool isActive;
  final DateTime? lastPaymentDate;
  final double? outstandingAmount;

  SocietyAccount({
    required this.accountId,
    required this.businessName,
    required this.societyName,
    required this.flatNumber,
    required this.ownerName,
    required this.email,
    this.phoneNumber,
    this.address,
    this.isActive = true,
    this.lastPaymentDate,
    this.outstandingAmount,
    super.id,
    super.createdAt,
    super.updatedAt,
    super.metadata,
  });

  factory SocietyAccount.fromJson(Map<String, dynamic> json) {
    return SocietyAccount(
      accountId: json['account_id'] ?? '',
      businessName: json['business_name'] ?? '',
      societyName: json['society_name'] ?? '',
      flatNumber: json['flat_number'] ?? '',
      ownerName: json['owner_name'] ?? '',
      email: json['email'] ?? '',
      phoneNumber: json['phone_number'],
      address: json['address'],
      isActive: JsonHelpers.parseBool(json['is_active']) ?? true,
      lastPaymentDate: JsonHelpers.parseDateTime(json['last_payment_date']),
      outstandingAmount: JsonHelpers.parseDouble(json['outstanding_amount']),
      id: json['id'],
      createdAt: JsonHelpers.parseDateTime(json['created_at']),
      updatedAt: JsonHelpers.parseDateTime(json['updated_at']),
      metadata: JsonHelpers.parseMap(json['metadata']),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'account_id': accountId,
      'business_name': businessName,
      'society_name': societyName,
      'flat_number': flatNumber,
      'owner_name': ownerName,
      'email': email,
      'phone_number': phoneNumber,
      'address': address,
      'is_active': isActive,
      'last_payment_date': JsonHelpers.dateTimeToJson(lastPaymentDate),
      'outstanding_amount': outstandingAmount,
      'id': id,
      'created_at': JsonHelpers.dateTimeToJson(createdAt),
      'updated_at': JsonHelpers.dateTimeToJson(updatedAt),
      'metadata': metadata,
    };
  }
}

/// Offline Payment Request extending BaseRequest
class OfflinePaymentRequest extends BaseRequest with ModelValidation {
  final String billId;
  final double amount;
  final String paymentMethod;
  final DateTime paymentDate;
  final String? referenceNumber;
  final String? bankName;
  final String? chequeNumber;
  final String? notes;
  final List<String> attachments;

  OfflinePaymentRequest({
    required this.billId,
    required this.amount,
    required this.paymentMethod,
    required this.paymentDate,
    this.referenceNumber,
    this.bankName,
    this.chequeNumber,
    this.notes,
    this.attachments = const [],
    super.id,
    super.createdAt,
    super.updatedAt,
    super.metadata,
  });

  factory OfflinePaymentRequest.fromJson(Map<String, dynamic> json) {
    return OfflinePaymentRequest(
      billId: json['bill_id'] ?? '',
      amount: JsonHelpers.parseDouble(json['amount']) ?? 0.0,
      paymentMethod: json['payment_method'] ?? '',
      paymentDate:
          JsonHelpers.parseDateTime(json['payment_date']) ?? DateTime.now(),
      referenceNumber: json['reference_number'],
      bankName: json['bank_name'],
      chequeNumber: json['cheque_number'],
      notes: json['notes'],
      attachments: JsonHelpers.parseList(
            json['attachments'],
            (item) => item.toString(),
          ) ??
          [],
      id: json['id'],
      createdAt: JsonHelpers.parseDateTime(json['created_at']),
      updatedAt: JsonHelpers.parseDateTime(json['updated_at']),
      metadata: JsonHelpers.parseMap(json['metadata']),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'bill_id': billId,
      'amount': amount,
      'payment_method': paymentMethod,
      'payment_date': JsonHelpers.dateTimeToJson(paymentDate),
      'reference_number': referenceNumber,
      'bank_name': bankName,
      'cheque_number': chequeNumber,
      'notes': notes,
      'attachments': attachments,
      'id': id,
      'created_at': JsonHelpers.dateTimeToJson(createdAt),
      'updated_at': JsonHelpers.dateTimeToJson(updatedAt),
      'metadata': metadata,
    };
  }

  @override
  bool validate() {
    final errors = getValidationErrors();
    return errors.isEmpty;
  }

  @override
  List<String> getValidationErrors() {
    final errors = <String>[];

    // Validate required fields
    final billIdValidation = Validators.required(billId, 'Bill ID');
    if (!billIdValidation.isValid) {
      errors.addAll(billIdValidation.errors);
    }

    final amountValidation = Validators.positive(amount, 'Amount');
    if (!amountValidation.isValid) {
      errors.addAll(amountValidation.errors);
    }

    final paymentMethodValidation =
        Validators.required(paymentMethod, 'Payment method');
    if (!paymentMethodValidation.isValid) {
      errors.addAll(paymentMethodValidation.errors);
    }

    // Validate payment date is not in future
    if (paymentDate.isAfter(DateTime.now())) {
      errors.add('Payment date cannot be in the future');
    }

    return errors;
  }

  @override
  bool requiresAuth() => true;

  @override
  int getTimeoutSeconds() => 30;
}

/// Maintenance Payment extending BaseModel
class MaintenancePayment extends BaseModel {
  final String paymentId;
  final String transactionId;
  final String billId;
  final double amount;
  final String status;
  final String paymentMethod;
  final DateTime paymentDate;
  final String? referenceNumber;
  final String? gatewayResponse;
  final Map<String, dynamic> paymentDetails;

  MaintenancePayment({
    required this.paymentId,
    required this.transactionId,
    required this.billId,
    required this.amount,
    required this.status,
    required this.paymentMethod,
    required this.paymentDate,
    this.referenceNumber,
    this.gatewayResponse,
    this.paymentDetails = const {},
    super.id,
    super.createdAt,
    super.updatedAt,
    super.metadata,
  });

  factory MaintenancePayment.fromJson(Map<String, dynamic> json) {
    return MaintenancePayment(
      paymentId: json['payment_id'] ?? '',
      transactionId: json['transaction_id'] ?? '',
      billId: json['bill_id'] ?? '',
      amount: JsonHelpers.parseDouble(json['amount']) ?? 0.0,
      status: json['status'] ?? '',
      paymentMethod: json['payment_method'] ?? '',
      paymentDate:
          JsonHelpers.parseDateTime(json['payment_date']) ?? DateTime.now(),
      referenceNumber: json['reference_number'],
      gatewayResponse: json['gateway_response'],
      paymentDetails: JsonHelpers.parseMap(json['payment_details']) ?? {},
      id: json['id'],
      createdAt: JsonHelpers.parseDateTime(json['created_at']),
      updatedAt: JsonHelpers.parseDateTime(json['updated_at']),
      metadata: JsonHelpers.parseMap(json['metadata']),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'payment_id': paymentId,
      'transaction_id': transactionId,
      'bill_id': billId,
      'amount': amount,
      'status': status,
      'payment_method': paymentMethod,
      'payment_date': JsonHelpers.dateTimeToJson(paymentDate),
      'reference_number': referenceNumber,
      'gateway_response': gatewayResponse,
      'payment_details': paymentDetails,
      'id': id,
      'created_at': JsonHelpers.dateTimeToJson(createdAt),
      'updated_at': JsonHelpers.dateTimeToJson(updatedAt),
      'metadata': metadata,
    };
  }
}

/// Maintenance Summary extending BaseModel
class MaintenanceSummary extends BaseModel {
  final double totalOutstanding;
  final double totalPaid;
  final int pendingBills;
  final int paidBills;
  final DateTime? lastPaymentDate;
  final double? lastPaymentAmount;
  final Map<String, double> monthlyBreakdown;

  MaintenanceSummary({
    required this.totalOutstanding,
    required this.totalPaid,
    required this.pendingBills,
    required this.paidBills,
    this.lastPaymentDate,
    this.lastPaymentAmount,
    this.monthlyBreakdown = const {},
    super.id,
    super.createdAt,
    super.updatedAt,
    super.metadata,
  });

  factory MaintenanceSummary.fromJson(Map<String, dynamic> json) {
    return MaintenanceSummary(
      totalOutstanding:
          JsonHelpers.parseDouble(json['total_outstanding']) ?? 0.0,
      totalPaid: JsonHelpers.parseDouble(json['total_paid']) ?? 0.0,
      pendingBills: JsonHelpers.parseInt(json['pending_bills']) ?? 0,
      paidBills: JsonHelpers.parseInt(json['paid_bills']) ?? 0,
      lastPaymentDate: JsonHelpers.parseDateTime(json['last_payment_date']),
      lastPaymentAmount: JsonHelpers.parseDouble(json['last_payment_amount']),
      monthlyBreakdown: Map<String, double>.from(
        (json['monthly_breakdown'] ?? {}).map(
          (key, value) => MapEntry(key, JsonHelpers.parseDouble(value) ?? 0.0),
        ),
      ),
      id: json['id'],
      createdAt: JsonHelpers.parseDateTime(json['created_at']),
      updatedAt: JsonHelpers.parseDateTime(json['updated_at']),
      metadata: JsonHelpers.parseMap(json['metadata']),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'total_outstanding': totalOutstanding,
      'total_paid': totalPaid,
      'pending_bills': pendingBills,
      'paid_bills': paidBills,
      'last_payment_date': JsonHelpers.dateTimeToJson(lastPaymentDate),
      'last_payment_amount': lastPaymentAmount,
      'monthly_breakdown': monthlyBreakdown,
      'id': id,
      'created_at': JsonHelpers.dateTimeToJson(createdAt),
      'updated_at': JsonHelpers.dateTimeToJson(updatedAt),
      'metadata': metadata,
    };
  }
}
